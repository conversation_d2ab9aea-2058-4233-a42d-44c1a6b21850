{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/vfile/vfile-message/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/unist": "^3.0.0", "unist-util-stringify-position": "^4.0.0"}, "description": "vfile utility to create a virtual message", "devDependencies": {"@types/node": "^24.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^1.0.0"}, "exports": "./index.js", "files": ["index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["file", "message", "utility", "util", "vfile-util", "vfile", "virtual"], "license": "MIT", "name": "vfile-message", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "vfile/vfile-message", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "prepack": "npm run build && npm run format", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "ignoreCatch": true}, "type": "module", "version": "4.0.3", "xo": {"prettier": true, "rules": {"prefer-destructuring": "off", "unicorn/prefer-at": "off"}, "space": true}}