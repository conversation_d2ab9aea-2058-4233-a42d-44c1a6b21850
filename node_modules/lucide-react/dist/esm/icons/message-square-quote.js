/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z",
      key: "18887p"
    }
  ],
  ["path", { d: "M14 13a2 2 0 0 0 2-2V9h-2", key: "zjz9hw" }],
  ["path", { d: "M8 13a2 2 0 0 0 2-2V9H8", key: "14e02x" }]
];
const MessageSquareQuote = createLucideIcon("message-square-quote", __iconNode);

export { __iconNode, MessageSquareQuote as default };
//# sourceMappingURL=message-square-quote.js.map
