/**
 * @license lucide-react v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 12H3", key: "18klou" }],
  ["path", { d: "M12 18H3", key: "11ftsu" }],
  ["path", { d: "M16 6H3", key: "1wxfjs" }],
  [
    "path",
    {
      d: "M21.033 14.44a.647.647 0 0 1 0 1.12l-4.065 2.352a.645.645 0 0 1-.968-.56v-4.704a.645.645 0 0 1 .968-.56z",
      key: "lh7ho8"
    }
  ]
];
const ListVideo = createLucideIcon("list-video", __iconNode);

export { __iconNode, ListVideo as default };
//# sourceMappingURL=list-video.js.map
