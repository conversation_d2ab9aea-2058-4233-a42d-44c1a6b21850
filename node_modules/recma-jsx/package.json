{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/mdx-js/recma/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "description": "recma plugin to add support for parsing and serializing JSX", "dependencies": {"acorn-jsx": "^5.0.0", "estree-util-to-js": "^2.0.0", "recma-parse": "^1.0.0", "recma-stringify": "^1.0.0", "unified": "^11.0.0"}, "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "homepage": "https://github.com/mdx-js/recma", "keywords": ["abstract", "ast", "compile", "javascript", "jsx", "plugin", "recma-plugin", "recma", "syntax", "tree", "unified"], "license": "MIT", "name": "recma-jsx", "repository": "https://github.com/mdx-js/recma/tree/main/packages/recma-jsx", "scripts": {}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}, "type": "module", "version": "1.0.1"}