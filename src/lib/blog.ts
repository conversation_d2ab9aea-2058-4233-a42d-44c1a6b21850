import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import readingTime from 'reading-time';
import { BlogPost, BlogCategory, CategorySlug } from '@/types/blog';

const contentDirectory = path.join(process.cwd(), 'content');

export const categories: BlogCategory[] = [
  {
    name: 'AWS',
    slug: 'aws',
    description: 'Amazon Web Services Security',
    color: 'bg-orange-500',
    count: 0
  },
  {
    name: 'Azure',
    slug: 'azure',
    description: 'Microsoft Azure Security',
    color: 'bg-blue-500',
    count: 0
  },
  {
    name: 'GCP',
    slug: 'gcp',
    description: 'Google Cloud Platform Security',
    color: 'bg-green-500',
    count: 0
  },
  {
    name: 'Penetration Testing',
    slug: 'penetration-testing',
    description: 'Ethical Hacking & Pen Testing',
    color: 'bg-red-500',
    count: 0
  }
];

export function getAllPosts(): BlogPost[] {
  const posts: BlogPost[] = [];

  categories.forEach(category => {
    const categoryPath = path.join(contentDirectory, category.slug);
    
    if (!fs.existsSync(categoryPath)) {
      return;
    }

    const files = fs.readdirSync(categoryPath);
    
    files.forEach(file => {
      if (file.endsWith('.md') || file.endsWith('.mdx')) {
        const filePath = path.join(categoryPath, file);
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const { data, content } = matter(fileContent);
        
        const slug = file.replace(/\.(md|mdx)$/, '');
        const readTime = readingTime(content);

        posts.push({
          slug,
          title: data.title || 'Untitled',
          description: data.description || '',
          date: data.date || new Date().toISOString(),
          category: category.slug,
          tags: data.tags || [],
          readingTime: readTime.text,
          image: data.image,
          content
        });
      }
    });
  });

  return posts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
}

export function getPostsByCategory(categorySlug: CategorySlug): BlogPost[] {
  return getAllPosts().filter(post => post.category === categorySlug);
}

export function getPostBySlug(categorySlug: CategorySlug, slug: string): BlogPost | null {
  const categoryPath = path.join(contentDirectory, categorySlug);
  const filePath = path.join(categoryPath, `${slug}.md`);
  const mdxFilePath = path.join(categoryPath, `${slug}.mdx`);
  
  let targetPath = '';
  if (fs.existsSync(filePath)) {
    targetPath = filePath;
  } else if (fs.existsSync(mdxFilePath)) {
    targetPath = mdxFilePath;
  } else {
    return null;
  }

  const fileContent = fs.readFileSync(targetPath, 'utf8');
  const { data, content } = matter(fileContent);
  const readTime = readingTime(content);

  return {
    slug,
    title: data.title || 'Untitled',
    description: data.description || '',
    date: data.date || new Date().toISOString(),
    category: categorySlug,
    tags: data.tags || [],
    readingTime: readTime.text,
    image: data.image,
    content
  };
}

export function getCategoriesWithCount(): BlogCategory[] {
  const posts = getAllPosts();
  
  return categories.map(category => ({
    ...category,
    count: posts.filter(post => post.category === category.slug).length
  }));
}

export function getAllTags(): string[] {
  const posts = getAllPosts();
  const tags = new Set<string>();
  
  posts.forEach(post => {
    post.tags.forEach(tag => tags.add(tag));
  });
  
  return Array.from(tags).sort();
}

export function getPostsByTag(tag: string): BlogPost[] {
  return getAllPosts().filter(post => 
    post.tags.some(postTag => postTag.toLowerCase() === tag.toLowerCase())
  );
}
